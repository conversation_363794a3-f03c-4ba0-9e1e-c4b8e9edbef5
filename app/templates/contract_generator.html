{% extends "base.html" %}

{% block title %}合同生成器{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 引入侧边栏模板 -->
    {% include 'sidebar.html' %}

    <!-- 主内容区 -->
    <div class="main-content">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
            <h1 class="h2">合同生成器</h1>
        </div>

        <!-- 显示提示消息 -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category if category != 'error' else 'danger' }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">填写合同信息</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
                            <div class="alert alert-info">
                                <i class="fas fa-money-bill-wave"></i>
                                <strong>价格明细：</strong>
                                <div class="row mt-3">
                                    <div class="col-md-6">
                                        <ul class="list-unstyled mb-0">
                                            <li class="mb-2"><strong>签约总价：</strong><span class="text-success">13,760元</span></li>
                                            <li class="mb-2"><strong>总租金：</strong><span class="text-primary">9,558元</span></li>
                                            <li class="mb-2"><strong>期数：</strong><span class="text-info">4（4个月）</span></li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <ul class="list-unstyled mb-0">
                                            <li class="mb-2"><strong>月租金：</strong><span class="text-warning">2,389.5元</span></li>
                                            <li class="mb-2"><strong>前期费用：</strong><span class="text-danger">590元（增值服务）+610元（履约保证金）=1,200元</span></li>
                                            <li class="mb-2"><strong>到期购买价（买断价）：</strong><span class="text-secondary">2,105元（第五个月需要支付），1,495元（第六个月需要支付）</span></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="version" class="form-label">版本选择</label>
                                <select class="form-select" id="version" name="version" required>
                                    <option value="新">新版本</option>
                                    <option value="旧">旧版本</option>
                                </select>
                                <div class="invalid-feedback">
                                    请选择版本
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="excel_file" class="form-label">上传Excel文件</label>
                                <input type="file" class="form-control" id="excel_file" name="excel_file" accept=".xls,.xlsx" required>
                                <div class="form-text">
                                    请上传包含订单数据的Excel文件（.xls 或 .xlsx 格式）
                                </div>
                                <div class="invalid-feedback">
                                    请选择Excel文件
                                </div>
                            </div>
                            
                            <!-- 附件勾选选项 -->
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="include_attachments" name="include_attachments" value="1" checked>
                                <label class="form-check-label" for="include_attachments">包含附件（《禁止变现承诺书》，《平台还款优先规则告知书》，《个人征信信息查询及报送授权书》，《租赁服务协议补充协议》）</label>
                            </div>
                            
                            <div class="alert alert-info mt-3">
                                <h6 class="alert-heading">文件要求：</h6>
                                <p class="mb-0">Excel文件需包含以下字段：订单ID、商品名称、商品规格颜色、收件人姓名、收件人手机号、收货地址、身份证号、总租金、总期数、起租日期、增值服务费、用户备注</p>
                            </div>
                            
                            <div class="d-grid mt-4">
                                <button type="submit" class="btn btn-primary" id="generateBtn">生成合同</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 加载中提示 -->
<div class="modal fade" id="loadingModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center p-4">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <h5>正在生成合同文件，请稍候...</h5>
                <p class="text-muted small">文件生成完成后将自动下载</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // 表单验证
    (function () {
        'use strict'
        
        // 页面加载完成时确保关闭全局加载指示器
        document.addEventListener('DOMContentLoaded', function() {
            // 确保加载指示器被隐藏
            if (typeof hideLoading === 'function') {
                hideLoading();
            }
            
            // 确保模态框加载指示器被隐藏
            var loadingModal = document.getElementById('loadingModal');
            if (loadingModal) {
                var bsModal = bootstrap.Modal.getInstance(loadingModal);
                if (bsModal) {
                    bsModal.hide();
                }
            }
        });
        
        // 获取所有需要验证的表单
        var forms = document.querySelectorAll('.needs-validation')
        
        // 阻止提交并应用验证样式
        Array.prototype.slice.call(forms)
            .forEach(function (form) {
                form.addEventListener('submit', function (event) {
                    if (!form.checkValidity()) {
                        event.preventDefault()
                        event.stopPropagation()
                    } else {
                        // 当表单验证通过并提交时，显示加载中
                        var loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
                        loadingModal.show();
                        
                        // 处理下载开始事件 - 多种方式尝试关闭加载提示
                        // 1. 设置超时自动关闭
                        setTimeout(function() {
                            loadingModal.hide();
                        }, 5000); // 5秒后自动关闭
                        
                        // 2. 监听窗口焦点变化
                        window.addEventListener('focus', function() {
                            setTimeout(function() {
                                loadingModal.hide();
                            }, 500);
                        }, { once: true });
                        
                        // 3. 监听beforeunload事件
                        window.addEventListener('beforeunload', function() {
                            loadingModal.hide();
                        }, { once: true });
                    }
                    
                    form.classList.add('was-validated')
                }, false)
            })
    })()
</script>
{% endblock %} 